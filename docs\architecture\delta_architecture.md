# DELTA Reasoning Architecture: Implementation Specification

**Project:** DELTA Reasoning Architecture
**Document Version:** v1.0
**Last Editor:** Engineering Team
**Date:** 2025-07-30

## Change Log

| Version | Date | Editor | Changes |
|---------|------|--------|---------|
| v1.0 | 2025-07-30 | Engineering Team | Initial implementation specification |

---

## Table of Contents

1. [Executive Summary and Implementation Overview](#1-executive-summary-and-implementation-overview)
   - 1.1 [Executive Summary](#11-executive-summary)
   - 1.2 [Implementation Overview](#12-implementation-overview)
   - 1.3 [Notation Key](#13-notation-key)
2. [High-Level Architecture and Operational Flow](#2-high-level-architecture-and-operational-flow)
   - 2.1 [High-Level Pipeline](#21-high-level-pipeline)
   - 2.2 [Component Responsibility Matrix](#22-component-responsibility-matrix)
   - 2.3 [Sequence Diagram](#23-sequence-diagram)
3. [Architectural Components and Formalisms](#3-architectural-components-and-formalisms)
4. [Complexity and Performance Analysis](#4-complexity-and-performance-analysis)
5. [Training and Evaluation](#5-training-and-evaluation)
6. [Engineering API Contracts](#6-engineering-api-contracts)
7. [Appendices](#7-appendices)

---

## 1. Executive Summary and Implementation Overview

### 1.1 Executive Summary
This document provides a complete implementation specification for the DELTA reasoning architecture. It is intended for an engineering team and leaves no design decisions open for interpretation. The architecture replaces standard token-by-token autoregressive generation with a three-phase cognitive process: **planning, thinking, and then speaking.**

### 1.2 Implementation Overview
The system is a **complete, standalone model**, not a component for integration into existing LLMs. An engineering team will build the full pipeline, which consists of three primary phases orchestrated by an `ExecutionController`:
1.  **The PromptSegmentationModule**: Analyzes the input prompt to create an `OperatorSequence` of distinct reasoning tasks.
2.  **The Conceptual Refinement Loop**: Iteratively refines each task's `LowRankOperator` using a `State -> Constraints -> Integration` process.
3.  **The TextGenerationModule**: Translates the final, stable `LowRankOperator` into coherent language.

This document provides the exact component designs, module interfaces, algorithms, and hyperparameters required for implementation.

### 1.3 Notation Key

The following mathematical notation is used consistently throughout this document:

| Symbol | Description | Type |
|--------|-------------|------|
| $\mathbf{h}_t$ | Hidden state at time step $t$ | Vector |
| $\mathbf{O}$ | Conceptual operator (low-rank) | Matrix |
| $\mathbf{A}, \mathbf{B}$ | Low-rank factorization matrices | Matrix |
| $\mathcal{L}$ | Loss function | Scalar |
| $d$ | Model dimension | Scalar |
| $r$ | Operator rank | Scalar |
| $k$ | Number of attractors/concepts | Scalar |
| $t$ | Time step index | Scalar |
| $i, j$ | Item indices | Scalar |
| $n$ | Refinement step index | Scalar |
| $b$ | Batch index | Scalar |

## 2. High-Level Architecture and Operational Flow

### 2.1 High-Level Pipeline
The DELTA architecture operates via a sequential execution loop managed by an `ExecutionController`. For a given prompt, the model executes the following high-level steps, as depicted in **Figure 1**.

**Figure 1: High-Level Operational Flow**
```mermaid
graph TD
    A[Start: User Prompt] --> B{Phase 1: Segmentation};
    B --> C[Operator Sequence Created: O_1, O_2, ...];

    subgraph "Execution Loop for each Operator O_i in Sequence"
        direction TB
        C --> D{Select Task O_i};
        D --> E["Phase 2: Refinement<br/>Constraint Integration Loop"];
        E --> F[Stable Operator O_i_final];
        F --> G["Phase 3: Generation<br/>Text Generation Module"];
        G --> H(Text chunk for Task i);
    end

    H --> C;
    C -- Sequence Empty --> I[Concatenate All Text Chunks];
    I --> J[Final Response];
```

### 2.2 Component Responsibility Matrix

**Table 1: Component Responsibilities**

| Component | Inputs | Outputs | Owner Module |
|-----------|--------|---------|--------------|
| **Prefill Processor** | Token embeddings $\{\mathbf{e}_1, ..., \mathbf{e}_L\}$ | Hidden states $\{\mathbf{h}_1, ..., \mathbf{h}_L\}$ | PromptSegmentationModule |
| **Boundary Detector** | Hidden states $\{\mathbf{h}_t\}$ | Boundary scores $\{s_t\}$ | PromptSegmentationModule |
| **ManifoldEmbedder** | Pooled hidden states $\bar{\mathbf{h}}_i$ | Manifold coordinates $\mathbf{c}_i$ | PromptSegmentationModule |
| **Operator Instantiator** | Pooled hidden states $\bar{\mathbf{h}}_i$ | Low-rank matrices $\mathbf{A}_i, \mathbf{B}_i$ | PromptSegmentationModule |
| **ConstraintGenerator** | State vector $\mathbf{V}_S$, semantic states | Force vectors $\mathbf{F}_{all}$, updated states | Refinement Loop |
| **StateIntegrator** | State $\mathbf{V}_S$, force vectors $\mathbf{F}_{all}$ | Integrated vector $\mathbf{V}_I$ | Refinement Loop |
| **TextGenerationModule** | Stable operator $\mathbf{O}_{final}$ | Token sequence $\{tok_1, ..., tok_T\}$ | ExecutionController |

### 2.3 Sequence Diagram

**Figure 2: Data Flow Sequence Diagram**
```mermaid
sequenceDiagram
    participant U as User
    participant EC as ExecutionController
    participant PSM as PromptSegmentationModule
    participant RL as RefinementLoop
    participant TGM as TextGenerationModule

    U->>EC: Input Prompt
    EC->>PSM: Process Prompt
    PSM->>PSM: Prefill Processing
    PSM->>PSM: Boundary Detection
    PSM->>PSM: Operator Instantiation
    PSM->>EC: EmbeddingSpace

    loop For each Operator in Sequence
        EC->>RL: Refine Operator
        RL->>RL: State Generation
        RL->>RL: Constraint Generator
        RL->>RL: State Integrator
        RL->>EC: Refined Operator
        EC->>TGM: Generate Text
        TGM->>EC: Text Chunk
    end

    EC->>U: Final Response
```

It is critical to understand that the DELTA architecture described herein is a **complete, standalone model,** not a component to be integrated into an existing LLM backbone like Llama or GPT. The entire system, from the PromptSegmentationModule's prefill pass to the TextGenerationModule's output, constitutes the full model.

References to a "stack" or layer-specific hidden states (e.g., $\mathbf{h}_{t,N}$) are legacies from the earlier `DeltaDecoder.md` concept. In this mature architecture, these concepts are abstracted away. The "Prefill Pass" should be understood as a single, sequential processing of the prompt to generate contextualized hidden states for the PromptSegmentationModule, not a deep, multi-layer computation.

### 2.4 Defining the "DELTA Block" in Context
While the DELTA architecture is not a stack of repeating blocks, the term **"DELTA Block"** or **"Refinement Block"** is used to refer to the specific functional unit that executes one step of the core constraint integration process within the **Conceptual Refinement Loop** (see **Algorithm 1**).

It is not a structural component like a Transformer block. Instead, it is the combination of the `ConstraintGenerator` and the `StateIntegrator`.

-   **Input**: A state vector ($\mathbf{V}_S$) and the current contextual states.
-   **Process**: It generates a set of constraint force vectors and integrates them with the state to produce a refined state.
-   **Output**: An integrated vector ($\mathbf{V}_I$) and the updated contextual states.

This functional block is the heart of the refinement phase, but it is called repeatedly within a loop on a single low-rank operator, rather than being stacked vertically to process a sequence of tokens.

## 3. Architectural Components and Formalisms

### 3.1 Phase 1: The PromptSegmentationModule and Operator Sequence

The PromptSegmentationModule's function is to create a high-level decomposition strategy for responding to the prompt. It operates on the outputs of a dedicated **Prefill Processor**.

-   **The Prefill Processor**: The prefill phase is executed by a dedicated **Prefill Processor**, a multi-layer recurrent network (e.g., GRU) that is a core component of the DELTA model. It processes the prompt's token embeddings sequentially to produce a sequence of contextualized hidden states $\{\mathbf{h}_1, \ldots, \mathbf{h}_L\}$. These hidden states serve two purposes: 1) they are fed to the boundary detection head to generate scores for the PromptSegmentationModule, and 2) the final hidden state of the Prefill Processor is used to initialize the states of the ContextualConstraints for the subsequent refinement phase.
The weights of the GRU layers are initialized using a standard method (e.g., Xavier uniform for weight matrices, zeros for biases).

-   **Mechanism: Boundary Detection**. As the model processes the prompt, a specialized head generates a **boundary score** $s_t \in [0, 1]$ for each token $t$.
    $$
    s_t = \sigma(\mathbf{w}_{b}^T \mathbf{h}_t + b_b)
    $$
    where $\mathbf{h}_t$ is the contextualized hidden state for token $t$ produced by the `Prefill Processor`. The parameters $(\mathbf{w}_{b}, b_b)$ are the weights of a simple `Linear` layer that projects the hidden state to a single logit, which is then passed through a `Sigmoid` function. These weights are trained via the SegmentationLoss ($\mathcal{L}_{\mathrm{seg}}$) to recognize semantic boundaries.

-   **Segmentation**. After the prefill, the sequence of scores is thresholded against a hyperparameter, `boundary_threshold`, to identify "split points," segmenting the prompt's hidden states $\{\mathbf{h}_1, \ldots, \mathbf{h}_L\}$ into $k$ conceptual chunks.
-   **Operator Instantiation**. For each chunk $j$ containing $m$ hidden states $\{\mathbf{h}_{j,1}, \ldots, \mathbf{h}_{j,m}\}$, a `LowRankOperator` $\mathbf{O}_j$ is instantiated. The instantiation is a learnable process:
    1.  The hidden states for the chunk are pooled into a single representative vector via mean pooling: $\bar{\mathbf{h}}_j = \frac{1}{m} \sum_{i=1}^m \mathbf{h}_{j,i}$.
    2.  This pooled vector is then projected by two separate, dedicated `Linear` layers ($\mathbf{W}_{\mathrm{inst},A}$, $\mathbf{W}_{\mathrm{inst},B}$) to generate the vectorized forms of the operator's low-rank matrices.
    $$
    \mathrm{vec}(\mathbf{A}_j) = \mathbf{W}_{\mathrm{inst},A} \bar{\mathbf{h}}_j
    $$
    $$
    \mathrm{vec}(\mathbf{B}_j) = \mathbf{W}_{\mathrm{inst},B} \bar{\mathbf{h}}_j
    $$
    The resulting flat vectors are then reshaped into matrices of size $(\mathrm{dim}(d) \times \mathrm{dim}(r))$. The weights of these instantiation layers ($\mathbf{W}_{\mathrm{inst},A}, \mathbf{W}_{\mathrm{inst},B}$) are initialized using a standard method like Xavier uniform initialization. This ensures that operators for different concepts are generated in a consistent, learnable manner, regardless of the number of tokens in their corresponding chunk.
**Manifold Construction**. The PromptSegmentationModule's ultimate goal is to construct an **EmbeddingSpace**, a low-dimensional geometric space ($\mathrm{dim}(d_{\mathrm{manifold}})$) that acts as a coordinate space.
    -   **Mechanism**: For each conceptual chunk $i$ with pooled hidden state $\bar{\mathbf{h}}_i$:
        1.  The `LowRankOperator` $\mathbf{O}_i$ is instantiated as before.
        2.  A dedicated **ManifoldEmbedder** (a learnable FFN) computes the concept's coordinates $\mathbf{c}_i \in \mathbb{R}^{d_{\mathrm{manifold}}}$ on the manifold.
            $$
            \mathbf{c}_i = \mathrm{ManifoldEmbedder}(\bar{\mathbf{h}}_i)
            $$
    -   **Relationship Encoding**: Relationships are encoded implicitly by the geometry.
        -   **Similarity**: The Euclidean distance $\|\mathbf{c}_i - \mathbf{c}_j\|$ represents semantic similarity. The model learns to place related concepts close together.
        -   **Dependency**: The `ManifoldEmbedder` can also be trained to output a displacement vector that points from a concept to its prerequisite, encoding dependencies directly into the manifold's vector field.
    -   **Training**: The `ManifoldEmbedder` is trained via a dedicated **ManifoldLoss** ($\mathcal{L}_{\mathrm{manifold}}$). This loss function encourages the learned geometry to match ground-truth relationships (e.g., minimizing distance for semantically similar concepts, aligning displacement vectors with known dependencies).
    -   **Output**: The final output of the PromptSegmentationModule is the `EmbeddingSpace`, a collection of `(Operator, Coordinates)` tuples.

#### 3.1.1 Formalizing the EmbeddingSpace
The construction of a meaningful `EmbeddingSpace` is not an emergent property but a direct result of optimizing the `ManifoldEmbedder` with a carefully formulated loss function, $\mathcal{L}_{\mathrm{manifold}}$. This loss function is a composite of two components, each enforcing a different geometric constraint on the manifold.

**Prerequisites**: The computation of this loss requires ground-truth relationship matrices for a set of $k$ concepts:
-   A **Similarity Matrix** $\mathbf{S} \in [0, 1]^{k \times k}$, where $S_{ij}$ is the semantic similarity between concepts $i$ and $j$. This can be pre-computed using sentence-embedding models on the concept descriptions.
-   A **Dependency Matrix** $\mathbf{D} \in \{0, 1\}^{k \times k}$, where $D_{ij} = 1$ if concept $j$ is logically dependent on concept $i$.

**Definition 1 (Similarity as Proximity Loss)**

This loss component enforces the principle that semantically similar concepts should be close to each other on the manifold, while dissimilar concepts should be far apart. We use a weighted contrastive loss formulation.

*   **Justification**: For any pair of concepts $(i, j)$ with coordinates $(\mathbf{c}_i, \mathbf{c}_j)$, we define their squared Euclidean distance as $d^2_{ij} = \|\mathbf{c}_i - \mathbf{c}_j\|^2$.
    -   If concepts $i$ and $j$ are similar ($S_{ij} \to 1$), the loss term $S_{ij} \cdot d^2_{ij}$ will be minimized when $d^2_{ij}$ is small. This creates an attractive force, pulling similar concepts together.
    -   If concepts $i$ and $j$ are dissimilar ($S_{ij} \to 0$), the loss term $(1 - S_{ij}) \cdot \max(0, m - d^2_{ij})$ dominates. This term is zero only if $d^2_{ij} \geq m$, where $m$ is a margin hyperparameter. This creates a repulsive force, pushing dissimilar concepts to be at least a distance of $\sqrt{m}$ apart.

*   **Formula**:
    $$
    \mathcal{L}_{\mathrm{sim}} = \frac{1}{k^2} \sum_{i=1}^k \sum_{j=1}^k \left[ S_{ij} \cdot d^2_{ij} + (1 - S_{ij}) \cdot \max(0, m - d^2_{ij}) \right]
    $$

**Definition 2 (Dependency as Directed Ordering Loss)**

This loss component enforces logical dependencies by imposing a directional order on the manifold. We designate a primary axis of the manifold (e.g., the first coordinate) as the "flow" or "time" axis. A concept must appear "after" its prerequisites along this axis.

*   **Justification**: For a dependency $i \to j$ ($D_{ij}=1$), we want the coordinate of $j$ on the primary axis, $c_{j,0}$, to be greater than the coordinate of $i$, $c_{i,0}$. The loss term $\max(0, c_{i,0} - c_{j,0} + \delta)$ is zero only if $c_{j,0} \geq c_{i,0} + \delta$, where $\delta$ is a small margin. This penalizes any arrangement where a concept appears before its prerequisite, forcing a valid logical ordering along the chosen axis.

*   **Formula**:
    $$
    \mathcal{L}_{\mathrm{dep}} = \frac{1}{\sum \mathbf{D}} \sum_{i=1}^k \sum_{j=1}^k D_{ij} \cdot \max(0, c_{i,0} - c_{j,0} + \delta)
    $$

**Lemma 1 (DAG Ordering Property)**
Enforcing $c_{j,0} \geq c_{i,0} + \delta$ for all dependencies $D_{ij} = 1$ produces a directed acyclic graph (DAG) ordering on the manifold's primary axis. The manifold can be traversed using topological sorting algorithms.

**Proof Sketch**: The constraint $c_{j,0} \geq c_{i,0} + \delta$ ensures strict ordering along the primary axis, preventing cycles. Any valid assignment of coordinates satisfying all constraints corresponds to a topological ordering of the dependency graph.

**Definition 3 (Total ManifoldLoss)**

The final manifold loss is the weighted sum of the similarity and dependency components. The weights $\alpha$ and $\beta$ are hyperparameters that balance the influence of these two geometric constraints.
$$
\mathcal{L}_{\mathrm{manifold}} = \alpha \mathcal{L}_{\mathrm{sim}} + \beta \mathcal{L}_{\mathrm{dep}}
$$
-   **Fallback Policy**: In the event that the PromptSegmentationModule does not detect any boundaries (i.e., the `OperatorSequence` would be empty), the system defaults to treating the entire prompt as a single conceptual chunk. A single `LowRankOperator` is instantiated from the pooled hidden states of the entire prompt, ensuring the model can always proceed with a decomposition.

### 3.2 The LowRankOperator

**Definition 4 (Low-Rank Operator)**
A mapping $\mathbf{O}: \mathbb{R}^d \to \mathbb{R}^d$ is low-rank-$r$ if $\exists \mathbf{A}, \mathbf{B} \in \mathbb{R}^{d \times r}$ such that $\forall \mathbf{x}, \mathbf{O}(\mathbf{x}) = \mathbf{A}(\mathbf{B}^T\mathbf{x})$.

A task representation is encoded as a `LowRankOperator`, a learnable linear transformation $\mathbf{O} \in \mathbb{R}^{d \times d}$ defined by two smaller matrices, $\mathbf{A}, \mathbf{B} \in \mathbb{R}^{d \times r}$, where $r \ll d$ is the rank.
$$
\mathbf{O} = \mathbf{A}\mathbf{B}^T \quad (\mathbf{A}, \mathbf{B} \in \mathbb{R}^{d \times r})
$$
This operator is not merely a static representation but an **addressable computational space**. Its basis vectors (the columns of $\mathbf{A}$ and $\mathbf{B}$) encode the structure of the task, which can be dynamically queried. The rank $r$ is a critical hyperparameter of the model, defined as `operator_rank_r`.

### 3.3 Phase 2: The Conceptual Refinement Loop
This is the refinement phase. For each operator $\mathbf{O}_i$ from the sequence, the model executes an internal optimization loop where the core `State -> Constraints -> Integration` process refines the operator itself. The initial state of the contextual constraints, $\mathbf{h}_{ctx}^{(0)}$, is derived from the final hidden state of the `Prefill Processor`. This initializes the refinement process with the full context of the user's prompt.

Before detailing the refinement loop, we define two key sub-components it employs for its selection mechanism:
-   **`StateEvaluator`**: A lightweight, non-autoregressive evaluation module used to generate a short, fixed-length assessment vector from a given state. It is architecturally identical to the main TextGenerationModule's GRU but is used to generate a fixed number of tokens (`evaluation_length`) to produce a hidden state `h_eval` representing a brief internal evaluation. This provides a computationally efficient, text-based representation of a state for assessment.
-   **`QualityEstimator`**: A simple feed-forward network that takes an augmented input (the concatenation of a state vector and its corresponding `h_eval` from the `StateEvaluator`) and outputs a single scalar logit. This logit represents the model's assessment of the quality or potential of that computational path. It is formally defined as part of the `ConstraintGenerator` in Section 3.3.2.

**Algorithm 1: Conceptual Refinement Loop**

**Preconditions:**
- Operator $\mathbf{O}^{(0)}$ initialized (rank $r$, dimension $d$)
- Semantic state $\mathbf{h}_{\mathrm{sem}}^{(0)} \in \mathbb{R}^d$
- Probe vectors $\{\mathbf{v}_{\mathrm{probe},b}\}_{b=1}^B$ with $\mathbf{v}_{\mathrm{probe},b} \in \mathbb{R}^d$
- $N_{\mathrm{refine}} > 0$, stability threshold $\tau_s > 0$, convergence threshold $\tau_c > 0$

**Input:** Initial operator $\mathbf{O}^{(0)}$, initial semantic attractor states $\mathbf{h}_{\mathrm{sem}}^{(0)}$, probe vectors $\{\mathbf{v}_{\mathrm{probe},b}\}_{b=1}^B$, steps $N_{\mathrm{refine}}$

**Output:** Refined operator $\mathbf{O}^{(n+1)}$, final semantic states $\mathbf{h}_{\mathrm{sem}}^{(n+1)}$, status $\in$ \{SUCCESS, DIVERGED, NEEDS\_SPLIT\}

**Postconditions:**
- Returns $(\mathbf{O}_{\mathrm{final}}, \mathbf{h}_{\mathrm{sem,final}}, \mathrm{status})$
- If status = SUCCESS: $\|\Delta_{\mathrm{op}}\|_F < \tau_c$ (converged)
- If status = DIVERGED: $\|\Delta_{\mathrm{op}}\|_F > \tau_s$ (unstable)
- If status = NEEDS\_SPLIT: loop completed without convergence

```pseudocode
1.  for n ∈ [0, N_refine-1] do:
2.      // Phase 1: Branching & Pre-selection
3.      theses ← [], entropies ← []
4.      for b ∈ [1, B] do:
5.          thesis ← O^(n)(v_probe,b)
6.          theses.append(thesis)
7.          logits ← W_vocab(LayerNorm(thesis))
8.          p ← Softmax(logits)
9.          entropy ← -∑_i p_i log(p_i)
10.         entropies.append(entropy)
11.     end for
12.     active_indices ← argsort(entropies)[:num_active_branches]
13.     active_theses ← theses[active_indices]
14.
15.     // Phase 2: Parallel Refinement
16.     synthesized_states ← [], next_sem_states ← []
17.     for thesis in active_theses do:
18.         F_all, h_sem,next ← AntithesisEngine(thesis, h_sem^(n))
19.         V_S ← SynthesisModule(thesis, F_all)
20.         synthesized_states.append(V_S)
21.         next_sem_states.append(h_sem,next)
22.     end for
23.
24.     // Phase 3: Selection & Voting
25.     confidences ← []
26.     for state in synthesized_states do:
27.         short_chain, h_mono ← InnerVerbalizer(state, inner_monologue_length)
28.         fluency_score ← LogProbability(short_chain)
29.         augmented_input ← concat(state, h_mono)
30.         judgment_score ← ConfidenceScorer(augmented_input)
31.         confidence_score ← judgment_score + fluency_score
32.         confidences.append(confidence_score)
33.     end for
34.     best_branch_idx ← argmax(confidences)
35.     V_S,best ← synthesized_states[best_branch_idx]
36.     V_T,best ← active_theses[best_branch_idx]
37.     h_sem^(n+1) ← next_sem_states[best_branch_idx]
38.
39.     // Phase 4: Loss Computation & Update
40.     L_correction ← ||V_S,best - V_T,best||²
41.     L_confidence ← -log_softmax(confidences)[best_branch_idx]
42.     L_update ← L_correction + w_conf · L_confidence
43.     Δ_op ← ∇_{A,B} L_update
44.
45.     // Phase 5: Stability Checks & Update
46.     if ||Δ_op||_F > refinement_stability_threshold then
47.         return O^(n), h_sem^(n+1), "DIVERGED"
48.     if ||Δ_op||_F < refinement_convergence_threshold then
49.         return O^(n), h_sem^(n+1), "SUCCESS"
50.     A^(n+1), B^(n+1) ← OptimizerStep(A^(n), B^(n), Δ_op)
51. end for
52. return O^(N_refine), h_sem^(N_refine), "NEEDS_SPLIT"
```

**Complexity Analysis:**
Each AntithesisEngine call updates $k$ attractors × $m$ sub-experts → $O(k \cdot m \cdot d^2)$.
Total per-step complexity: $O(B \cdot d \cdot r + k \cdot m \cdot d^2 + \mathrm{num\_active\_branches} \cdot d \cdot V)$
where $V$ is vocabulary size.

**Proof.** Each iteration processes $B$ probe vectors ($O(B \cdot d \cdot r)$), runs AntithesisEngine on active branches ($O(\mathrm{num\_active\_branches} \cdot k \cdot m \cdot d^2)$), and performs vocabulary projection for confidence scoring ($O(\mathrm{num\_active\_branches} \cdot d \cdot V)$). The dominant term is typically the AntithesisEngine computation.

**Termination Proof:**
The convergence criterion $\|\Delta_{\mathrm{op}}\|_F < \tau_c$ together with the stability threshold $\tau_s$ bounds the Frobenius norm of updates. Since the loss function $\mathcal{L}_{\mathrm{update}}$ is continuous and the operator space is bounded, the sequence $\{\mathbf{O}^{(n)}\}$ must either converge (SUCCESS), become unstable (DIVERGED), or exhaust the iteration limit (NEEDS\_SPLIT) within $N_{\mathrm{refine}}$ steps.

This process elevates the dialectic from a simple feedback loop to an **efficient, ensemble-based deliberative engine.** At each step, the model explores multiple "what-if" scenarios by generating candidate theses. It then performs a cheap **pre-selection** by calculating the verbalization entropy of each thesis, culling the most ambiguous or "unclear" thoughts. Only the most promising candidates proceed to the expensive parallel refinement stage. The model then "votes" on the *outcomes* of these refined paths by using a lightweight **`InnerVerbalizer`** and a **`ConfidenceScorer`** to gauge the coherence and quality of each synthesized state. The `ConceptualOperator` is then updated based on the winning path, driven by both internal consistency ($\mathcal{L}_{correction}$) and the drive to make one reasoning path clearly superior ($\mathcal{L}_{confidence}$). This two-stage selection process forces the model to efficiently "debate" the best path forward.

The `OptimizerStep` function (line 49) refers to a single step of a dedicated, lightweight optimizer instance (e.g., Adam). This optimizer is instantiated anew for each `ConceptualOperator` at the beginning of its refinement loop. Its state (e.g., momentum buffers for Adam) is preserved across the $N_{refine}$ steps for that operator, ensuring stable convergence, and then discarded. It updates only the parameters of the current operator's matrices using the `refinement_lr` and other optimizer-specific hyperparameters (e.g., `refinement_optimizer_betas`).

#### 3.3.1. The Learnable Probe Vector
The "Thesis Generation via Probing" step in Algorithm 1 relies on a set of **learnable probe vectors,** $\{\mathbf{v}_{probe,b}\}$. These components are crucial for initiating the deliberative refinement process.

-   **Definition**: The probe vectors are a set of `B` learnable parameters of the model, shared across all refinement loops. They are initialized once using a standard method (e.g., Xavier uniform initialization) and updated via back-propagation.
-   **Function**: Their role is to provide diverse starting points for the "what-if" exploration. Each probe vector learns to "ask" about the concept from a different angle, generating a set of candidate theses for the inner monologue phase.
-   **Rationale**: Using multiple learnable probes is critical for deliberation. The model learns to optimize the set of probes to generate a useful diversity of initial thoughts, which provides a rich substrate for the confidence-based selection process.

#### 3.3.2. The Dialectic Engine: Antithesis and Synthesis
The `AntithesisEngine` and `SynthesisModule` functions in Algorithm 1 are the core of the dialectic process. Their mechanisms are detailed here.

##### The Antithesis Engine
The Antithesis Engine's purpose is to generate a set of potential constraints, or "forces," that challenge the Thesis ($\mathbf{V}_T$). It does this using a collection of specialized "attractors" organized into distinct domains.

1.  **Domains of Reasoning**: The architecture is specified with two primary domains, but is extensible.
    *   **Structural Domain**: A bank of stateless attractors that learn to represent context-free rules (e.g., logic, grammar, syntax).
    *   **Semantic Domain**: A bank of stateful **Semantic Attractors** that track context and represent semantic constraints.
    *   **Extensibility**: The design allows for the addition of more domains (e.g., a "Stylistic" domain for controlling tone, or a "Persona" domain) by simply adding new banks of attractors. The `SynthesisModule` would seamlessly incorporate their force vectors into its cross-attention mechanism.

2.  **Attractor Types**: Each domain contains a set of `num_attractors_k` experts, or attractors, with distinct mechanisms:
    *   **Structural Attractors (Stateless)**: These are implemented as fixed, learnable **Low-Rank Operator Attractors**. They apply a stateless transformation to the Thesis. The matrices $\mathbf{A}_i, \mathbf{B}_i$ are parameters of the model, initialized using Xavier uniform initialization.
        $$
        \mathbf{f}_{struct,i} = \tanh((\mathbf{A}_i \mathbf{B}_i^T) \mathbf{V}_T)
        $$
    *   **Semantic Attractors (Stateful)**: To provide a rich "vocabulary" of contextual patterns, each of the `k$ semantic experts is implemented as a **Composite Semantic Attractor**. This is a mixture-of-experts (MoE) model at the attractor level. The initial hidden states for all GRU cells are initialized to zero vectors at the start of inference. The GRU cells' weights are initialized with Xavier uniform, and the gating network's weight matrix $\mathbf{W}_{gate,i}$ is also initialized with Xavier uniform.
        
        For each of the `k` composite attractors, and for each of its `m` sub-experts (`num_sub_experts_m`):
        1.  **Parallel State Update**: All `m` internal GRU cells, each with a hidden size of `d_model`, update their hidden states based on the Thesis:
            $$
            \mathbf{h}_{sub\_out_{i,j}} = \text{GRU}_{i,j}(\mathbf{V}_T, \mathbf{h}_{sub\_in_{i,j}}) \quad \text{for } j \in [1, m]
            $$
        2.  **Dynamic Gating**: A gating network computes weights for each of the `m` updated sub-states based on the Thesis:
            $$
            \mathbf{g}_{sem,i} = \text{Softmax}(\mathbf{W}_{gate,i} \mathbf{V}_T) \in \mathbb{R}^m
            $$
        3.  **Composite State Formation**: The sub-states are combined into a single, rich contextual vector:
            $$
            \mathbf{h}_{composite,i} = \sum_{j=1}^{m} (\mathbf{g}_{sem,i})_j \cdot \mathbf{h}_{sub\_out_{i,j}}
            $$
        4.  **Dynamic Operator Instantiation**: This composite state is used to instantiate the operator's matrices:
            $$
            \text{vec}(\mathbf{A}_{sem,i}) = \mathbf{W}_{sem,A} \mathbf{h}_{composite,i}
            $$
            $$
            \text{vec}(\mathbf{B}_{sem,i}) = \mathbf{W}_{sem,B} \mathbf{h}_{composite,i}
            $$
        5.  The final force is computed using this state-dependent operator:
            $$
            \mathbf{f}_{sem,i} = \tanh((\mathbf{A}_{sem,i} \mathbf{B}_{sem,i}^T) \mathbf{V}_T)
            $$

3.  **Confidence Scorer**: The `AntithesisEngine` also contains a lightweight, learnable confidence head. This component provides the real-time judgment used in the deliberative refinement loop.
    *   **Mechanism**: It is a simple feed-forward network that takes an **augmented input**—the concatenation of a candidate Thesis vector and its corresponding verbalized thought vector (`h_mono`)—and projects it to a single scalar (a confidence logit).
    *   **Formulation**:
        $$
        \text{confidence_logit} = \text{FFN}(\text{LayerNorm}(\text{concat}(\mathbf{V}_T, \mathbf{h}_{mono})))
        $$

    *   **Distinction from Antithesis**: It is crucial to distinguish the role of the `ConfidenceScorer` from the `AntithesisEngine`'s attractors. The attractors generate a high-dimensional set of *corrective force vectors* intended to directly modify the Thesis. The `ConfidenceScorer` generates a single *scalar judgment* intended to guide the selection between different candidate Theses.

        | Component | Role | Output | Analogy |
        | :--- | :--- | :--- | :--- |
        | **Antithesis Engine** | **Correction**: "How should this thought be fixed?" | Set of vectors ($\mathbf{F}_{all}$) | A team of editors providing specific, directional feedback. |
        | **Confidence Scorer** | **Selection**: "Which of these thoughts is better?" | Scalar score | A lead editor choosing which draft is more promising to work on. |

4.  **Output**: The engine computes the force vector $\mathbf{f}_i$ from each of its attractors across all domains in parallel. It does **not** combine them. The final output is a tuple containing: 1) a tensor $\mathbf{F}_{all} \in \mathbb{R}^{(\text{num\_domains} \times \text{num\_attractors\_k}) \times d}$ of the stacked raw force vectors, and 2) the set of updated semantic attractor states, $\mathbf{h}_{sem, out}$.

##### The Synthesis Module
The Synthesis Module resolves the tension between the Thesis $\mathbf{V}_T$ and the set of raw forces $\mathbf{F}_{all}$ from the Antithesis Engine. The canonical method for this advanced architecture is **Direct Cross-Attention Synthesis**, as it is most synergistic with the goal of dynamic, interpretable reasoning.

-   **Mechanism**: This method bypasses the creation of a single composite Antithesis vector. Instead, the Thesis $\mathbf{V}_T$ acts as a **query** to directly attend to the pool of expert force vectors $\mathbf{F}_{all}$, which serve as both Keys and Values. The model learns to dynamically select and compose the most relevant constraints for the current reasoning step.
-   This method uses a standard **Multi-Head Cross-Attention** mechanism. The Thesis $\mathbf{V}_T$ acts as the Query, while the force vectors $\mathbf{F}_{all}$ serve as both Keys and Values. The number of attention heads is a configurable hyperparameter. The "relevance" of each force vector is determined by the dot-product similarity between the Thesis (projected into a Query) and the force vectors (projected into Keys). The outputs of all heads are concatenated and passed through a final linear projection.
-   **Formulation**: Let $H$ be the number of attention heads (`synthesis_attention_heads`).
    $$ \mathbf{V}_{S,raw} = \text{MultiHeadCrossAttention}(\mathbf{q}=\mathbf{V}_T, \mathbf{k}=\mathbf{F}_{all}, \mathbf{v}=\mathbf{F}_{all}) $$
    The final synthesized state is a residual connection:
    $$
    \mathbf{V}_{S} = \text{LayerNorm}(\mathbf{V}_T + \mathbf{V}_{S,raw})
    $$
-   **Rationale**: This approach offers maximum flexibility and interpretability. The attention weights provide a clear signal of which expert constraints the model is using to refine its thought. It replaces a fixed, hierarchical gating structure with a more powerful, dynamic composition mechanism, which is philosophically aligned with the overall architecture. The output, $\mathbf{V}_{S}$, is then used in the operator update step as described in Algorithm 1.

### 3.4 Phase 3: The "Reader" Verbalizer
This is the "speaking" phase. The Verbalizer is a simple recurrent module (e.g., a GRU cell) that translates a stable operator $\mathbf{O}_{final}$ into text. Its GRU cell is initialized using a standard method (e.g., Xavier uniform for weights, zeros for biases). It does not "guess" words; it "reads out" the operator's contents.

**Algorithm 2: Verbalization Loop**

**Preconditions:**
- Stable operator $\mathbf{O}_{\mathrm{final}}: \mathbb{R}^d \to \mathbb{R}^d$ (low-rank-$r$)
- Token embedding matrix $\mathbf{W}_{\mathrm{embed}} \in \mathbb{R}^{V \times d}$
- Vocabulary projection matrix $\mathbf{W}_{\mathrm{vocab}} \in \mathbb{R}^{d \times V}$
- Maximum sequence length $T_{\max} > 0$

**Input:** Stable operator $\mathbf{O}_{\mathrm{final}}$, embedding matrix $\mathbf{W}_{\mathrm{embed}}$, projection matrix $\mathbf{W}_{\mathrm{vocab}}$

**Output:** Token sequence $T_{\mathrm{chunk}}$, status $\in$ \{COMPLETED, TIMEOUT\}

**Postconditions:**
- If status = COMPLETED: sequence ends with EOS token, length $\leq T_{\max}$
- If status = TIMEOUT: sequence length = $T_{\max}$, no EOS token found
- All tokens $\in [0, V-1]$ (valid vocabulary indices)

```pseudocode
1.  h_verb,0 ← 0 ∈ ℝᵈ
2.  tok_0 ← BOS_ID
3.  for t ∈ [0, T_max-1] do:
4.      // Update Reading Head
5.      h_verb,t+1 ← GRU(W_embed(tok_t), h_verb,t)
6.      // Probe the Operator
7.      v_probed ← O_final(h_verb,t+1)
8.      // Project to Vocabulary
9.      logits ← W_vocab(LayerNorm(v_probed))
10.     // Sample Next Token
11.     tok_t+1 ← Sample(logits)
12.     if tok_t+1 == EOS_ID then break
13. end for
14. if t < T_max-1 then
15.     return {tok_1, ..., tok_t+1}, "COMPLETED"
16. else
17.     return {tok_1, ..., tok_T_max}, "TIMEOUT"
```

**Complexity Analysis:**
Per-token complexity: $O(d \cdot r + d^2 + d \cdot V)$
- Operator application: $O(d \cdot r)$ (low-rank structure)
- GRU update: $O(d^2)$ (hidden state dimension)
- Vocabulary projection: $O(d \cdot V)$

**Proof.** Each iteration performs one GRU update ($O(d^2)$), one low-rank operator application ($O(d \cdot r)$), and one vocabulary projection ($O(d \cdot V)$). The total per-token cost is dominated by the vocabulary projection when $V$ is large.

**Termination Proof:**
The algorithm terminates in at most $T_{\max}$ steps. Either an EOS token is sampled (COMPLETED) or the maximum length is reached (TIMEOUT). Since sampling is stochastic but bounded, termination is guaranteed.

The Verbalizer's recurrent state $\mathbf{h}_{verb}$ acts as a "reading head" or pointer. The coherence between the Verbalizer and the Operator is not assumed to emerge spontaneously; it is **enforced by the end-to-end Verbalization Loss ($\mathcal{L}_{verb}$)**. During training, gradients from this loss flow back to both the Verbalizer's parameters and the Operator's instantiation layers. This joint optimization compels the Verbalizer to learn a useful *trajectory of probes* and the Operator to learn a corresponding *semantic landscape* that, together, minimize the final text prediction error.This "reading" mechanism, which translates a stable, refined concept, is a primary architectural defense against hallucination, as it constrains the Verbalizer to the semantic space defined by the final operator.

### 3.5 The Phase Manager
The `PhaseManager` is the high-level control logic that orchestrates the workflow. Its implementation follows the procedure defined below, which we will refer to as Algorithm 3.

**Algorithm 3: Phase Manager Logic (Inference)**

**Preconditions:**
- User prompt $P$ is a valid token sequence
- Model $M$ is fully initialized with trained parameters
- All hyperparameters are set (manifold radius, $T_{\max}$, etc.)

**Input:** User prompt $P$

**Output:** Final generated text $T_{\mathrm{final}}$

**Postconditions:**
- $T_{\mathrm{final}}$ is a concatenation of text chunks in original prompt order
- All operators in the manifold have been processed
- Semantic states are updated consistently

```pseudocode
1.  // Phase 1: Planning
2.  Manifold, h_sem_initial ← M.plan(P)
3.  processing_order ← FindTraversalPath(Manifold)
4.  original_prompt_order ← Manifold.get_operators_by_prompt_order()
5.
6.  // Initialize state tracking
7.  final_texts ← {} // Map: operator_id → text_chunk
8.  final_operators ← {} // Map: operator_id → refined_operator
9.  h_sem_current ← h_sem_initial
10.
11. // Execution Loop (following manifold path)
12. for O_current in processing_order do:
13.     // Cohesion Step: neighbor influence
14.     neighbor_ops ← Manifold.get_neighbors(O_current.coords, radius, final_operators)
15.     O_cohesive ← M.apply_field_cohesion(O_current, neighbor_ops)
16.
17.     // Phase 2: Refinement
18.     O_final, h_sem_current, refine_status ← M.refine(O_cohesive, h_sem_current)
19.
20.     if refine_status == "DIVERGED" then
21.         log("Warning: Refinement diverged. Proceeding with last stable state.")
22.     else if refine_status == "NEEDS_SPLIT" then
23.         log("Info: Concept too complex. Articulating and re-planning.")
24.         internal_prompt, _ ← M.verbalize(O_final, max_length=32)
25.         new_concepts, h_sem_current ← M.plan(internal_prompt, h_sem_current,
26.                                              center_coord=O_current.coords)
27.         Manifold.remove_point(O_current)
28.         Manifold.add_points(new_concepts)
29.         processing_order ← FindTraversalPath(Manifold)
30.         continue
31.     end if
32.
33.     // Phase 3: Verbalization
34.     T_chunk, verb_status ← M.verbalize(O_final, max_length=T_max)
35.     if verb_status == "TIMEOUT" then
36.         log("Warning: Verbalization timeout for operator " + O_current.id)
37.
38.     // Store results
39.     final_texts[O_current.id] ← T_chunk
40.     final_operators[O_current.id] ← O_final
41. end for
42.
43. // Reconstruct final output in original order
44. ordered_texts ← [final_texts[op.id] for op in original_prompt_order]
45. T_final ← concatenate(ordered_texts)
46. return T_final
47. ```
48.
49. **Complexity Analysis:**
50. Let $k$ be the number of operators in the manifold. The complexity is:
51. - Planning: $O(L \cdot d^2)$ where $L$ is prompt length
52. - Refinement per operator: $O(N_{\mathrm{refine}} \cdot k \cdot m \cdot d^2)$ (see Algorithm 1)
53. - Verbalization per operator: $O(T_{\max} \cdot (d \cdot r + d \cdot V))$ (see Algorithm 2)
54. - Total: $O(L \cdot d^2 + k \cdot N_{\mathrm{refine}} \cdot k \cdot m \cdot d^2 + k \cdot T_{\max} \cdot d \cdot V)$
55.
56. **Proof.** The algorithm processes each operator exactly once (unless NEEDS_SPLIT occurs, which is bounded by re-planning limits). Each processing step involves refinement and verbalization with complexities as analyzed in Algorithms 1 and 2.
57.
58. **Termination Proof:**
59. The algorithm terminates when all operators in the processing order have been handled. The NEEDS_SPLIT case can potentially add new operators, but this is bounded by maximum re-planning depth to prevent infinite recursion.

Note: This algorithm describes the inference path. The Lifelong Learning cycle (imprinting, promotion) is typically handled by a separate, asynchronous process that is triggered after a generation is complete and its reward has been determined.

### 3.6 Tokenization and Vocabulary
The DELTA architecture, like any language model, requires a well-defined tokenizer and vocabulary.

-   **Tokenizer**: The recommended tokenizer is **SentencePiece** using a **Byte-Pair Encoding (BPE)** model. This provides a good balance between vocabulary size and the ability to handle out-of-vocabulary words.
-   **Vocabulary Size (`V`)**: The size of the vocabulary is a key hyperparameter (`vocab_size`). It determines the dimensions of the token embedding matrix (`W_embed`) and the final vocabulary projection layer (`W_vocab`). The embedding matrix `W_embed` is initialized from a normal distribution (e.g., $N(0, 0.02)$), while the vocabulary projection layer `W_vocab` is initialized with Xavier uniform.
-   **Special Tokens**: The vocabulary must include the following special tokens:
    -   `[BOS]` (Begin of Sequence): Used to initialize the Verbalizer's generation loop (`BOS_ID`).
    -   `[EOS]` (End of Sequence): The token the Verbalizer aims to generate to signal completion (`EOS_ID`).
## 4. Complexity and Performance Analysis
### 4.1 Computational Complexity (Time)

-   **Planning (Prefill)**: The prefill is performed by a multi-layer GRU (the `Prefill Processor`) with `prefill_gru_layers` layers. The complexity is therefore **$O(\text{prefill\_gru\_layers} \cdot L \cdot d^2)$**. This is linear with respect to the prompt length $L$.
-   **Refinement (per step)**: The complexity of a single step within the refinement loop is the sum of its components:
    -   **Antithesis Engine**: The dominant cost is the `num_attractors_k` Composite Semantic Attractors. Each involves `m` GRU updates ($O(m \cdot d^2)$) and dynamic operator instantiation ($O(d^2 \cdot r)$). Total: $O(\text{num\_attractors\_k} \cdot (m \cdot d^2 + d^2 \cdot r))$.
    -   The overall per-step complexity is therefore dominated by the Semantic Attractors: $O(\text{num\_attractors\_k} \cdot (m \cdot d^2 + d^2 \cdot r))$.
-   **Verbalization**: Each token generation step involves a GRU update, one operator application ($O(d \cdot r)$), and a projection to the vocabulary. This is highly efficient, with a per-token complexity of approximately $O(d \cdot r + d \cdot V)$, where $V$ is the vocabulary size.

The architecture trades higher initial latency (for planning and refinement) for faster and more robust per-token generation.

### 4.2 Memory Complexity

-   **Static Memory**: The memory required for model parameters is determined by the architectural hyperparameters (`d_model`, `operator_rank_r`, `num_attractors_k`, etc.) and is constant.
-   **Dynamic Memory (Inference)**:
    -   **Activation Memory**: Standard memory usage for storing activations during the forward pass.
    -   **Conceptual Agenda**: For a batch of size $B$, with an average of $k$ operators per prompt, the memory to store the operator matrices is $O(B \cdot k \cdot d \cdot r)$.

## 5. Engineering API Contracts

This section provides strict typed interfaces for all modules in the DELTA architecture, including input/output shapes, data types, and configuration specifications.

### 5.1 Core Module Interfaces

#### 5.1.1 Planner Module

```python
class Planner(nn.Module):
    def forward(
        self,
        prompt_tokens: Tensor,           # shape (batch_size, seq_len), dtype=long
        attention_mask: Optional[Tensor] = None  # shape (batch_size, seq_len), dtype=bool
    ) -> Tuple[ConceptualManifold, Tensor]:
        """
        Args:
            prompt_tokens: Input token IDs
            attention_mask: Mask for padding tokens (True for real tokens)

        Returns:
            manifold: ConceptualManifold containing operators and coordinates
            h_sem_init: Initial semantic states, shape (batch_size, d_model), dtype=float32
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'd_model': {'default': 512, 'range': [128, 2048], 'tunable_runtime': False},
            'boundary_threshold': {'default': 0.5, 'range': [0.1, 0.9], 'tunable_runtime': True},
            'd_manifold': {'default': 64, 'range': [16, 256], 'tunable_runtime': False},
            'prefill_gru_layers': {'default': 2, 'range': [1, 4], 'tunable_runtime': False}
        }
```

#### 5.1.2 Prefill Processor Module

```python
class PrefillProcessor(nn.Module):
    def forward(
        self,
        token_embeddings: Tensor,        # shape (batch_size, seq_len, d_model), dtype=float32
        attention_mask: Optional[Tensor] = None  # shape (batch_size, seq_len), dtype=bool
    ) -> Tensor:
        """
        Args:
            token_embeddings: Embedded input tokens
            attention_mask: Mask for padding tokens

        Returns:
            hidden_states: Contextualized hidden states,
                          shape (batch_size, seq_len, d_model), dtype=float32
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'gru_hidden_size': {'default': 512, 'range': [128, 2048], 'tunable_runtime': False},
            'num_layers': {'default': 2, 'range': [1, 6], 'tunable_runtime': False},
            'dropout': {'default': 0.1, 'range': [0.0, 0.5], 'tunable_runtime': True}
        }
```

#### 5.1.3 Cartographer Module

```python
class Cartographer(nn.Module):
    def forward(
        self,
        pooled_hidden_states: Tensor     # shape (batch_size, num_concepts, d_model), dtype=float32
    ) -> Tensor:
        """
        Args:
            pooled_hidden_states: Mean-pooled hidden states for each concept

        Returns:
            coordinates: Manifold coordinates,
                        shape (batch_size, num_concepts, d_manifold), dtype=float32
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'd_manifold': {'default': 64, 'range': [16, 256], 'tunable_runtime': False},
            'hidden_dim': {'default': 256, 'range': [128, 1024], 'tunable_runtime': False},
            'num_layers': {'default': 2, 'range': [1, 4], 'tunable_runtime': False}
        }
```

#### 5.1.4 Antithesis Engine Module

```python
class AntithesisEngine(nn.Module):
    def forward(
        self,
        thesis: Tensor,                  # shape (batch_size, d_model), dtype=float32
        semantic_states: Tensor          # shape (batch_size, num_attractors_k, d_model), dtype=float32
    ) -> Tuple[Tensor, Tensor]:
        """
        Args:
            thesis: Current thesis vector
            semantic_states: Current semantic attractor states

        Returns:
            force_vectors: All force vectors,
                          shape (batch_size, num_domains * num_attractors_k, d_model), dtype=float32
            updated_states: Updated semantic states,
                           shape (batch_size, num_attractors_k, d_model), dtype=float32
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'num_attractors_k': {'default': 8, 'range': [4, 32], 'tunable_runtime': False},
            'num_sub_experts_m': {'default': 4, 'range': [2, 16], 'tunable_runtime': False},
            'num_domains': {'default': 2, 'range': [1, 4], 'tunable_runtime': False},
            'operator_rank_r': {'default': 64, 'range': [16, 256], 'tunable_runtime': False}
        }
```

#### 5.1.5 Synthesis Module

```python
class SynthesisModule(nn.Module):
    def forward(
        self,
        thesis: Tensor,                  # shape (batch_size, d_model), dtype=float32
        force_vectors: Tensor            # shape (batch_size, num_forces, d_model), dtype=float32
    ) -> Tensor:
        """
        Args:
            thesis: Current thesis vector
            force_vectors: Force vectors from AntithesisEngine

        Returns:
            synthesized_vector: Refined vector after synthesis,
                               shape (batch_size, d_model), dtype=float32
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'synthesis_attention_heads': {'default': 8, 'range': [4, 16], 'tunable_runtime': False},
            'attention_dropout': {'default': 0.1, 'range': [0.0, 0.3], 'tunable_runtime': True}
        }
```

#### 5.1.6 Verbalizer Module

```python
class Verbalizer(nn.Module):
    def forward(
        self,
        operator: LowRankOperator,       # Low-rank operator (rank r, dimension d)
        max_length: int = 512,           # Maximum sequence length
        temperature: float = 1.0         # Sampling temperature
    ) -> Tuple[Tensor, str]:
        """
        Args:
            operator: Stable conceptual operator to verbalize
            max_length: Maximum tokens to generate
            temperature: Sampling temperature

        Returns:
            token_ids: Generated token sequence, shape (seq_len,), dtype=long
            status: Generation status ("COMPLETED" or "TIMEOUT")
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'gru_hidden_size': {'default': 512, 'range': [128, 2048], 'tunable_runtime': False},
            'vocab_size': {'default': 32000, 'range': [8000, 100000], 'tunable_runtime': False},
            'max_sequence_length': {'default': 512, 'range': [64, 2048], 'tunable_runtime': True}
        }
```

#### 5.1.7 Phase Manager Module

```python
class PhaseManager(nn.Module):
    def forward(
        self,
        prompt: str,                     # Input prompt string
        **kwargs                         # Additional generation parameters
    ) -> str:
        """
        Args:
            prompt: User input prompt
            **kwargs: Additional parameters (temperature, max_length, etc.)

        Returns:
            generated_text: Final generated response
        """
        pass

    def get_config_keys(self) -> Dict[str, Any]:
        return {
            'manifold_radius': {'default': 2.0, 'range': [0.5, 5.0], 'tunable_runtime': True},
            'max_replanning_depth': {'default': 3, 'range': [1, 10], 'tunable_runtime': True},
            'cohesion_weight': {'default': 0.1, 'range': [0.0, 1.0], 'tunable_runtime': True}
        }
```

### 5.2 Data Types and Structures

#### 5.2.1 ConceptualManifold

```python
@dataclass
class ConceptualManifold:
    operators: List[LowRankOperator]     # List of conceptual operators
    coordinates: Tensor                  # shape (num_concepts, d_manifold), dtype=float32
    operator_ids: List[str]              # Unique identifiers for each operator
    dependency_matrix: Optional[Tensor]  # shape (num_concepts, num_concepts), dtype=bool

    def get_neighbors(self, coords: Tensor, radius: float,
                     processed_ops: Dict[str, LowRankOperator]) -> List[LowRankOperator]:
        """Find neighboring operators within radius"""
        pass

    def get_operators_by_prompt_order(self) -> List[LowRankOperator]:
        """Return operators in original prompt order"""
        pass
```

#### 5.2.2 LowRankOperator

```python
@dataclass
class LowRankOperator:
    A: Tensor                           # shape (d_model, operator_rank_r), dtype=float32
    B: Tensor                           # shape (d_model, operator_rank_r), dtype=float32

    def __call__(self, x: Tensor) -> Tensor:
        """Apply operator: O(x) = A @ (B^T @ x)"""
        return self.A @ (self.B.T @ x)

    @property
    def rank(self) -> int:
        return self.A.shape[1]

    @property
    def dimension(self) -> int:
        return self.A.shape[0]
```

### 5.3 Configuration Management

All modules must implement the `get_config_keys()` method returning a dictionary with the following structure:

```python
{
    'parameter_name': {
        'default': <default_value>,
        'range': [<min_value>, <max_value>],  # For numeric parameters
        'tunable_runtime': <bool>,  # Whether parameter can be changed during inference
        'description': <str>  # Optional human-readable description
    }
}
```

## 6. Training and Evaluation

The training and evaluation procedures for the DELTA architecture are designed to handle its multi-part loss function and distinct operational phases.

### 6.1 Training Pipeline Overview

The DELTA training process follows a structured 5-step pipeline:

**Step 0: Data Preparation**
- Load and preprocess training data into required format
- Apply tokenization and create attention masks
- Generate ground-truth relationship matrices

**Step 1: Planning Forward Pass & Losses**
- Execute Prefill Processor on input prompts
- Compute boundary detection scores
- Calculate Planner Loss ($\mathcal{L}_{\mathrm{plan}}$) and Placement Loss ($\mathcal{L}_{\mathrm{place}}$)

**Step 2: Refinement & Verbalization Forward Pass & Losses**
- Run Conceptual Refinement Loop for each operator
- Execute Verbalizer on refined operators
- Calculate Convergence Loss ($\mathcal{L}_{\mathrm{conv}}$) and Verbalization Loss ($\mathcal{L}_{\mathrm{verb}}$)

**Step 3: Loss Aggregation & Backward Pass**
- Combine all losses with appropriate weights
- Perform backward pass with gradient routing
- Apply gradient clipping

**Step 4: Optimizer Step**
- Update model parameters using AdamW optimizer
- Apply learning rate scheduling
- Log training metrics

### 6.2 Training Data Format

The model is trained on a dataset where each example consists of a tuple: `(prompt, boundary_targets, dependency_matrix, similarity_matrix, target_texts)`.

**Table 2: Training Data Components**

| Component | Type | Shape | Description |
|-----------|------|-------|-------------|
| `prompt` | str | - | Input text string |
| `boundary_targets` | Tensor | (seq_len,) | Binary labels marking conceptual chunk boundaries |
| `dependency_matrix` | Tensor | (k, k) | Logical dependencies between concepts |
| `similarity_matrix` | Tensor | (k, k) | Semantic similarity scores between concepts |
| `target_texts` | List[str] | (k,) | Ground-truth text for each conceptual chunk |

The `source/scripts/preprocess_data.py` script converts existing instruction-following or chain-of-thought datasets into this format.

### 6.3 Batch Processing and Loss Computation

For each batch of training examples, the following steps are performed:

**Step 1: Planning Phase**
- Run the **Prefill Phase** for all prompts in the batch to get hidden states and boundary scores
- Compute the **Planner Loss** ($\mathcal{L}_{\mathrm{plan}}$) using the predicted scores and `boundary_targets`
- Segment the hidden states based on the **ground-truth** `boundary_targets` to create the concepts for each example. This is a form of teacher-forcing that prevents the downstream modules from being penalized for an incorrect plan
- Compute the **Placement Loss** ($\mathcal{L}_{\mathrm{place}}$) using the predicted coordinates and the ground-truth relationship matrices
- Instantiate the `ConceptualOperator`s for each agenda

**Step 2: Refinement and Verbalization Phase**
- Initialize empty lists for verbalization losses and convergence losses
- For each example in the batch, iterate through its `ConceptualAgenda`:
  a. Run the **Conceptual Refinement Loop** on the operator
  b. Compute the **Convergence Loss** ($\mathcal{L}_{\mathrm{conv}}$) for this operator
  c. Run the **Verbalizer** on the final, refined operator
  d. Compute the **Verbalization Loss** ($\mathcal{L}_{\mathrm{verb}}$) for the generated text against the corresponding `target_text`

**Step 3: Total Loss Aggregation**
- Average the losses across the batch and combine them into the final loss for the step:
  $$\mathcal{L}_{\mathrm{total}} = w_1\mathcal{L}_{\mathrm{verb}} + w_2\mathcal{L}_{\mathrm{plan}} + w_3\mathcal{L}_{\mathrm{conv}} + w_4\mathcal{L}_{\mathrm{place}}$$

### 6.4 PyTorch Training Loop Skeleton

```python
# Training loop implementation skeleton
class DeltaTrainer:
    def __init__(self, model: DeltaModel, config: TrainingConfig):
        self.model = model
        self.config = config
        self.optimizer = AdamW(model.parameters(), lr=config.learning_rate)
        self.scheduler = CosineAnnealingLR(self.optimizer, T_max=config.max_steps)
        self.gradient_router = GradientRouter()

    def training_step(self, batch: Dict[str, Tensor]) -> Dict[str, float]:
        self.optimizer.zero_grad()

        # Step 1: Planning Phase
        manifold, h_sem_init = self.model.planner(
            batch['prompt_tokens'],
            batch['attention_mask']
        )

        # Compute planning losses with gradient routing
        boundary_scores = self.model.boundary_detector(
            self.gradient_router.for_planner_loss(batch['hidden_states'])
        )
        loss_plan = F.binary_cross_entropy_with_logits(
            boundary_scores, batch['boundary_targets']
        )

        coordinates = self.model.cartographer(
            self.gradient_router.for_placement_loss(batch['pooled_states'])
        )
        loss_place = self.compute_placement_loss(
            coordinates, batch['similarity_matrix'], batch['dependency_matrix']
        )

        # Step 2: Refinement and Verbalization
        loss_conv_total = 0.0
        loss_verb_total = 0.0

        for operator in manifold.operators:
            # Refinement
            refined_op, _, _ = self.model.refinement_loop(
                self.gradient_router.for_convergence_loss(operator), h_sem_init
            )
            loss_conv_total += self.compute_convergence_loss(operator, refined_op)

            # Verbalization
            generated_tokens, _ = self.model.verbalizer(refined_op)
            loss_verb_total += F.cross_entropy(
                generated_tokens, batch['target_tokens']
            )

        # Step 3: Loss aggregation
        total_loss = (
            self.config.w_verb * loss_verb_total +
            self.config.w_plan * loss_plan +
            self.config.w_conv * loss_conv_total +
            self.config.w_place * loss_place
        )

        # Step 4: Backward pass and optimization
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)
        self.optimizer.step()
        self.scheduler.step()

        return {
            'total_loss': total_loss.item(),
            'loss_verb': loss_verb_total.item(),
            'loss_plan': loss_plan.item(),
            'loss_conv': loss_conv_total.item(),
            'loss_place': loss_place.item()
        }
```

### 6.5 Benchmark Expectations

**Table 3: Expected Performance Ranges**

| Benchmark | Model Size | Expected Accuracy | Notes |
|-----------|------------|-------------------|-------|
| **GSM8K** | 220M params | 35-40% | Mathematical reasoning baseline |
| **GSM8K** | 1B params | 55-65% | With proper refinement training |
| **MATH** | 220M params | 15-20% | Complex mathematical problems |
| **MATH** | 1B params | 25-35% | Benefit from multi-step reasoning |
| **Big-Bench Hard** | 220M params | 40-45% | Multi-domain reasoning |
| **Big-Bench Hard** | 1B params | 60-70% | Strong conceptual planning advantage |
| **MMLU** | 220M params | 45-50% | General knowledge baseline |
| **MMLU** | 1B params | 65-75% | Structured reasoning helps |

**Performance Validation Checklist:**
- [ ] GSM8K accuracy > 35% at 220M parameters (sanity check)
- [ ] Refinement loop converges within N_refine steps > 90% of cases
- [ ] Boundary detection F1 score > 0.7 on validation set
- [ ] Manifold placement loss decreases consistently during training
- [ ] Verbalization timeout rate < 5% during inference

### 5.3. Gradient Management and Optimization

-   **Main Optimizer**: The overall model parameters (all weights *except* those of the per-operator refinement optimizer) are trained using a standard optimizer like **AdamW**. The learning rate is managed by a scheduler, typically a **cosine decay schedule with a linear warmup phase**, to ensure stable convergence.
-   **Gradient Routing**: To ensure functional specialization, gradient flow is controlled using `torch.detach()`:
    *   The **Verbalization Loss** is the primary driver. Its gradients flow back through the Verbalizer, the Refinement Loop (affecting the Dialectic Engine), and the Operator Instantiation layers.
    *   The **Planner Loss** gradient should only update the boundary-scoring head. The hidden states used to compute the scores are detached from the rest of the computation graph for this loss calculation.
    *   The **Convergence Loss** gradient should only update the components of the `AntithesisEngine` and `SynthesisModule`. The initial operator state is detached when calculating this loss.
    *   The **Placement Loss** gradient should only update the `PlacementHead`. The pooled hidden states used as input to the head are detached for this loss calculation.
-   **Backpropagation**: A single call to `L_total.backward()` is performed. The selective use of `.detach()` during the forward pass ensures that the computed gradients are routed to the correct components. This logic can be encapsulated in a helper class like `GradientRouter` for cleaner implementation.
-   **Gradient Clipping and Monitoring**: After the backward pass and before the optimizer step, global gradient norm clipping should be applied. The total norm of the gradients should be logged after clipping to monitor for potential instability (e.g., consistently high or spiking norms).

#### 5.3.1. GradientRouter Implementation Concept
To implement the gradient routing cleanly, a helper class or context manager can be used. The `GradientRouter` would manage the `.detach()` calls based on which loss is being computed.

```python
# Conceptual implementation for c:\Dev\Projects\delta\source\delta\learning\gradient_router.py
class GradientRouter:
    @staticmethod
    def for_planner_loss(hidden_states):
        # Detach hidden states so only the planner head is trained
        return hidden_states.detach()

    @staticmethod
    def for_convergence_loss(initial_operator):
        # Detach the initial operator so only the dialectic engine is trained
        # by this loss to encourage convergence.
        detached_A = initial_operator.A.detach()
        detached_B = initial_operator.B.detach()
        # Create a new operator with detached tensors
        return LowRankOperator(A=detached_A, B=detached_B)

    @staticmethod
    def for_placement_loss(pooled_hidden_states):
        # Detach pooled hidden states so only the PlacementHead is trained
        return pooled_hidden_states.detach()
```
This approach encapsulates the logic and makes the main training loop cleaner.

### 5.4. Loss Formulations Revisited

-   **Verbalization Loss ($\mathcal{L}_{verb}$)**: The primary global loss, training the model to produce correct text.
    $$ \mathcal{L}_{verb} = \sum_{i=1}^k \text{CrossEntropy}(\text{Verbalizer}(\mathbf{O}_{i,final}), \text{target\_text}_i) $$

-   **Planner Loss ($\mathcal{L}_{plan}$)**: A local loss for the boundary detection head. To handle class imbalance, a weighted Binary Cross-Entropy is used. The `pos_weight` should be computed dynamically for each batch to accurately reflect the imbalance: `pos_weight = num_negative_samples / num_positive_samples`.
    $$ \mathcal{L}_{plan} = \text{BCEWithLogitsLoss}(\text{boundary\_logits}, \text{target\_boundaries}, \text{pos\_weight}=\text{dynamic\_pos\_weight}) $$

-   **Placement Loss ($\mathcal{L}_{place}$)**: A local loss for the `PlacementHead`. As defined in Section 3.1.1, this is a composite loss that trains the model to generate a geometrically meaningful mind map.
    $$ \mathcal{L}_{place} = \alpha \mathcal{L}_{sim} + \beta \mathcal{L}_{dep} $$

-   **Convergence Loss ($\mathcal{L}_{conv}$)**: This loss acts as a **regularizer.** Its purpose is to encourage the refinement process to find a stable fixed point, preventing chaotic oscillations. It does not provide a directional signal for *what* the concept should be, but rather encourages the dialectic process to "settle." Its weight, $w_3$, should typically be small to avoid overpowering the main learning signal from `L_verb` and causing the refinement updates to vanish prematurely.
    $$ \mathcal{L}_{conv} = \sum_{i=1}^k \sum_{n=1}^{N_{refine}} \left( \|\mathbf{A}_i^{(n+1)} - \mathbf{A}_i^{(n)}\|_F^2 + \|\mathbf{B}_i^{(n+1)} - \mathbf{B}_i^{(n)}\|_F^2 \right) $$

### 5.5. Batching and Padding Strategy

Since different prompts in a batch can produce a variable number of conceptual operators, a padding strategy is required for the refinement and verbalization phases.

1.  **Find Max Length**: After the planning phase, determine the maximum number of operators, `k_max`, for any example in the batch.
2.  **Pad Operator Lists**: For each example, pad its list of `ConceptualOperator`s with a dummy/placeholder operator up to `k_max`.
3.  **Create Attention Mask**: Create a boolean attention mask of shape `(batch_size, k_max)` that is `True` for real operators and `False` for padded ones.
4.  **Batched Processing**: The refinement and verbalization loops can now operate on tensors of shape `(batch_size, k_max, ...)`.
5.  **Masked Loss**: The final `Verbalization Loss` and `Convergence Loss` must be computed using the attention mask to ensure that padded operators do not contribute to the loss.

### 5.6. Distributed Training Strategy

For large-scale training, the DELTA architecture is compatible with standard data-parallel frameworks like PyTorch's `DistributedDataParallel` (DDP).
-   **Mechanism**: DDP will wrap the entire DELTA model. During the backward pass, it will automatically synchronize gradients for all shared parameters (e.g., `Prefill Processor`, `AntithesisEngine` attractors, `Verbalizer`) across all devices.
-   **Refinement Optimizer**: The per-operator optimizer used in the refinement loop is instantiated and used within the forward pass. As it does not modify the model's shared parameters directly, its state does not need to be synchronized by DDP, simplifying the distributed setup.

### 5.7. Evaluation Strategy

This section outlines a recommended strategy for evaluating the DELTA architecture, focusing on benchmarks and metrics that align with its core goals of planning, reasoning, and constraint adherence.

#### 5.7.1. Recommended Benchmarks

A comprehensive evaluation should cover multiple facets of reasoning. The following benchmarks are recommended:

| Benchmark Category | Example Benchmarks | Rationale |
| :--- | :--- | :--- |
| **Mathematical Reasoning** | GSM8K, MATH | Directly tests multi-step logical deduction and numerical reasoning, which should benefit from the refinement loop. |
| **Commonsense & Multi-step Reasoning** | Big-Bench Hard (BBH), MMLU | Assesses the model's ability to handle complex, multi-domain problems that require breaking down a question and reasoning through it. |
| **Logical Reasoning** | LogiQA, ReClor | Evaluates performance on formal logical puzzles and reading comprehension tasks that require identifying logical relationships. |
| **Planning & Instruction Following** | Custom benchmarks from datasets like `databricks-dolly-15k` or `CoT` collections. | Crucial for evaluating the `Planner`'s ability to correctly segment complex, multi-part prompts into a coherent `ConceptualAgenda`. |
| **Constraint Adherence** | Custom "Challenge Sets" | Measures the model's ability to follow explicit negative or stylistic constraints (e.g., "Describe a scene without using verbs of motion"). |

A crucial part of evaluation is comparing DELTA's performance against strong, publicly available **baseline models** of similar scale (e.g., Llama 2, Mistral, Mamba). This provides a clear measure of the architecture's practical advantages and disadvantages.

#### 5.7.2. Key Evaluation Metrics

Beyond standard task accuracy, evaluating the internal components of DELTA is crucial for understanding its behavior.

| Metric | Component | Description |
| :--- | :--- | :--- |
| **Task Accuracy** | End-to-End | The primary metric (e.g., exact match accuracy) on the chosen benchmarks. |
| **Planner F1-Score**| Planner | The F1-score for boundary detection (treating it as a sequence tagging task) on the annotated validation set. This measures the Planner's structural correctness. |
| **Refinement Convergence Rate** | Refinement Loop | The average number of refinement steps taken before the stability threshold is met. A lower number indicates more efficient "thinking". |
| **Constraint Failure Rate** | End-to-End | The percentage of outputs that violate explicit constraints on the custom challenge sets. This directly measures a core architectural goal. |
| **Time to First Token (TTFT)** | Inference | The wall-clock time from receiving the prompt to generating the first token. This measures the combined latency of the Planner and the first operator's refinement loop. |
| **Time Per Output Token (TPOT)** | Inference | The average wall-clock time to generate each subsequent token after the first. This measures the efficiency of the Verbalizer. |
| **Human Preference Score** | End-to-End | For qualitative aspects like coherence and faithfulness to complex instructions, a score derived from human A/B testing against a baseline model is the gold standard. |

#### 5.7.3. Recommended Ablation Studies

To validate the contribution of DELTA's novel components, a series of ablation studies should be performed:

| Ablation | Description | Hypothesis |
| :--- | :--- | :--- |
| **No Planner** | The entire prompt is treated as a single conceptual chunk. | Performance on multi-part instructions will degrade significantly, but performance on single-task prompts may be unaffected. |
| **No Refinement** | Set `N_refine` to 0. The initial operator is passed directly to the Verbalizer. | Performance on complex reasoning tasks (e.g., GSM8K) will drop sharply, as the model loses its "thinking" phase. |
| **No Dialectic Engine** | Replace the `AntithesisEngine` and `SynthesisModule` with a simple residual connection. | The model will lose its ability to perform structured constraint checking, likely increasing hallucinations and logical errors. |

## 6. Key Hyperparameters

A successful implementation of this architecture requires careful tuning of several key hyperparameters. This process should be systematic, leveraging experiment tracking tools like **Weights & Biases (WandB)** or **MLflow** to log metrics and visualize results. Automated hyperparameter optimization frameworks like **Optuna** or **Ray Tune** can be used to efficiently search the parameter space.

| Hyperparameter | Section | Default / Range | Tuning Strategy |
| :--- | :--- | :--- | :--- |
| `vocab_size` | 3.6 | `32000` / `[32k, 128k]` | Determined by the tokenizer training process. Not typically tuned post-training. |
| `d_model` | Global | `1024` / `[768, 4096]` | Foundational model dimension. Not typically tuned; set based on desired model scale (e.g., 1B, 7B params) and hardware constraints. |
| `prefill_gru_layers` | 3.1 | `2` / `[1, 4]` | Number of layers in the `Prefill Processor`. More layers increase contextualization capacity at the cost of prefill time. |
| `boundary_threshold` | 3.1 | `0.5` / `[0.3, 0.9]` | Tune to maximize the `Planner F1-Score` on a validation set. This directly balances precision and recall for segmentation. |
| `operator_rank_r` | 3.2 | `32` / `[16, 128]` | Trade-off: Higher rank increases conceptual capacity but also memory/compute. Increase until performance on complex reasoning benchmarks (e.g., GSM8K) plateaus. |
| `num_attractors_k` | 3.3.2 | `8` / `[4, 16]` | Trade-off: More attractors allow for more specialized reasoning patterns but increase refinement cost. Monitor performance on diverse benchmarks like MMLU. |
| `num_sub_experts_m` | 3.3.2 | `4` / `[2, 8]` | Increases contextual richness of semantic attractors. Tune based on performance on tasks requiring deep contextual understanding. |
| `attractor_rank_r` | 3.3.2 | `16` / `[8, 64]` | Capacity of the fixed structural attractors. Lower cost than `operator_rank_r`; can be increased more freely. |
| `synthesis_attention_heads` | 3.3.2 | `8` / `[4, 16]` | Standard MHA parameter. Should scale with `d_model`. Little tuning is usually required if a standard value is chosen. |
| `N_refine` | 3.3 | `5` / `[1, 10]` | Key latency vs. performance trade-off. Monitor the `Refinement Convergence Rate`. If the model consistently converges early, `N_refine` can be lowered. |
| `refinement_stability_threshold` | 3.3 | `100.0` / `[10.0, 1e3]` | Safeguard. Set high initially. If refinement divergence is logged frequently, lower this value to be more restrictive. |
| `refinement_convergence_threshold` | 3.3 | `1e-4` / `[1e-5, 1e-3]` | Controls early stopping. A smaller value means more "thinking" but higher latency. Tune in conjunction with `N_refine`. |
| `num_probe_vectors_B` | 3.3 | `3` / `[2, 5]` | The number of learnable probe vectors (`B`) used to generate candidate theses. This defines the number of "what-if" branches to explore. |
| `num_active_branches` | 3.3 | `2` / `[1, num_refinement_branches]` | The number of top branches to keep after entropy pre-selection. Must be <= `num_refinement_branches`. |
| `inner_monologue_length` | 3.3 | `2` / `[1, 5]` | The number of tokens to generate for each inner monologue. |
| `refinement_confidence_weight` | 3.3 | `0.1` / `[0.01, 0.5]` | Weight for the confidence-boosting loss in the refinement loop. |
| `refinement_lr` | 3.3 | `1e-3` / `[1e-4, 1e-2]` | Highly sensitive. Perform a small grid search, monitoring the `Convergence Loss` on a validation set. The ideal value should decrease this loss without making it vanish too quickly. |
| `refinement_optimizer` | 3.3 | `'Adam'` / `['Adam', 'SGD']` | Adam is a robust default. No tuning is typically needed unless refinement is highly unstable. |
| `refinement_optimizer_betas` | 3.3 | `(0.9, 0.999)` | Standard Adam parameters. Generally do not require tuning. |
| `w1, w2, w3` | 4.2 | `(1.0, 0.2, 0.01)` | `w1` should be `1.0`. Tune `w2` to ensure `Planner F1-Score` improves. `w3` should be kept small to act as a light regularizer. |
| `main_lr` | 4.2.3 | `1e-4` / `[1e-5, 6e-4]` | The peak learning rate for the main AdamW optimizer, used by the scheduler. |
| `main_weight_decay` | 4.2.3 | `0.1` | Standard weight decay for the main optimizer. |
| `lr_scheduler_type` | 4.2.3 | `'cosine'` / `['cosine', 'linear']` | The type of learning rate scheduler for the main optimizer. |
| `lr_warmup_steps` | 4.2.3 | `2000` / `[500, 10000]` | Number of linear warmup steps for the learning rate scheduler. |
| `lr_min_lr` | 4.2.3 | `1e-5` / `[1e-6, 1e-5]` | The minimum learning rate for the cosine decay scheduler. |
| `gradient_clip_val` | 4.2.3 | `1.0` | The value for global gradient norm clipping. A standard default. |
| `num_inactive_attractors` | 5.2 | `16` / `[8, 64]` | The size of the pool of inactive attractors available for promotion. Defines the model's capacity for new permanent skills. |
| `knowledge_base_capacity` | 5.1 | `100` / `[50, 500]` | The maximum number of short-term operators to store in the `KnowledgeBase` before eviction. |
| `verbalizer_sampling_strategy` | 3.4 | `'greedy'` / `['greedy', 'top_p']` | Runtime choice. Use 'greedy' for deterministic evaluation and 'top_p' for more creative generation. |
| `verbalizer_temperature` | 3.4 | `1.0` / `[0.1, 2.0]` | Runtime choice. Use `< 1.0` for more focused output, `> 1.0` for more diverse output. Not used with 'greedy'. |
| `promotion_threshold` | 5.2 | `5` / `[3, 20]` | Controls adaptation speed. A low value allows for rapid learning but risks promoting spurious patterns. Tune based on long-term performance on a fixed benchmark. |
| `demotion_threshold` | 5.3 | `10` / `[5, 25]` | Controls forgetting. Should be `> promotion_threshold`. A high value makes the model's skills very stable but less adaptable. |
| `reinforcement_threshold` | 5.1 | `0.1` / `[0.05, 0.3]` | Tune based on the observed distribution of attention weights in the `SynthesisModule` during successful generations. |
| `attribution_threshold` | 5.3 | `0.1` / `[0.05, 0.3]` | Tune based on the observed distribution of attention weights in the `SynthesisModule` during failed generations. |

## 7. Lifelong Learning: The Skill Lifecycle

The architecture supports a sophisticated, three-stage process for inference-time learning that allows the model to acquire, generalize, and discard skills over time. This avoids direct weight mutation, ensuring stability, and provides a robust path to lifelong learning.

### 7.1. Stage 1: Short-Term Memory (Imprinting)

This first stage allows the model to learn from its immediate successes within a single conversational session.

1.  **Success Detection**: After a generation is deemed successful (via the external Reward Model), the final, stable `ConceptualOperator` ($\mathbf{O}_{final}$) that produced it is identified.
2.  **Knowledge Base Structure**: The `KnowledgeBase` is an in-memory dictionary mapping operator IDs to objects containing the operator's tensors (`A`, `B`), a success counter, and a usage timestamp. To prevent unbounded growth, it is capped at `knowledge_base_capacity`. If the capacity is exceeded, the least recently used operator is evicted.
3.  **Imprinting**: The new operator is added to the `KnowledgeBase`.
    $$
    \text{KnowledgeBase} \leftarrow \text{KnowledgeBase} \cup \{(\mathbf{O}_{final}, \text{counter}=1)\}
    $$
4.  **Serialization**: For persistence across sessions, the `KnowledgeBase` should be serialized. A recommended approach is to save the tensor data for all operators into a single file (e.g., using `torch.save`) and the associated metadata (counters, timestamps, session ID) into a separate JSON file.
5.  **Usage & Reinforcement**: During subsequent reasoning steps, the `AntithesisEngine` is augmented. It probes each operator in the `KnowledgeBase` to generate additional force vectors. If an imprinted operator receives an attention weight above the `reinforcement_threshold` during another successful generation, its counter is incremented and its usage timestamp is updated.

### 7.2. Stage 2: Long-Term Memory (Promotion)

This second stage provides a mechanism for generalizing frequently used, successful reasoning patterns into permanent skills.

1.  **Promotion Threshold**: The model has a hyperparameter, `promotion_threshold`. When an operator's success counter in the `KnowledgeBase` reaches this threshold, it is marked for promotion.
2.  **Inactive Attractor Pool**: The `AntithesisEngine` is initialized with a number of "inactive" or "uninitialized" `Low-Rank Operator Attractor` slots.
3.  **Promotion Policy**: When an operator is promoted:
    *   If an inactive attractor slot is available, it is used.
    *   If all slots are full, a **replacement policy** is enacted: the active attractor with the **highest failure count** is selected for replacement. In case of a tie, the least recently used (LRU) attractor among the tied set is chosen. "Recent use" is tracked via a `last_used_timestamp` (e.g., a global step counter) updated whenever the attractor is influential in a generation.
    *   The `A` and `B` matrices from the successful `ConceptualOperator` are copied into the parameters of the selected slot.
    *   The chosen attractor is marked as "active," its counters are reset, and its `last_used_timestamp` is updated.
    *   The original operator is removed from the `KnowledgeBase`.

## 7. Appendices

### Appendix A: Hyperparameter Configuration

**Table A1: Complete Hyperparameter Reference**

| Category | Parameter | Default | Range | Tuning Priority | Description |
|----------|-----------|---------|-------|-----------------|-------------|
| **Model Architecture** | `d_model` | 1024 | [768, 4096] | Low | Base model dimension |
| | `operator_rank_r` | 32 | [16, 128] | High | Low-rank operator capacity |
| | `num_attractors_k` | 8 | [4, 16] | Medium | Number of semantic attractors |
| **Planning** | `boundary_threshold` | 0.5 | [0.3, 0.9] | High | Concept segmentation threshold |
| | `d_manifold` | 64 | [16, 256] | Medium | Manifold coordinate dimension |
| **Refinement** | `N_refine` | 5 | [1, 10] | High | Maximum refinement steps |
| | `refinement_lr` | 1e-3 | [1e-4, 1e-2] | High | Inner-loop learning rate |
| | `num_active_branches` | 2 | [1, 5] | Medium | Active thesis branches |
| **Training** | `main_lr` | 1e-4 | [1e-5, 6e-4] | High | Main optimizer learning rate |
| | `w_verb` | 1.0 | Fixed | Low | Verbalization loss weight |
| | `w_plan` | 0.2 | [0.1, 0.5] | Medium | Planning loss weight |
| | `w_conv` | 0.01 | [0.005, 0.05] | Medium | Convergence loss weight |

**Recommended Sweep Plan:**
1. **First Priority**: `boundary_threshold`, `operator_rank_r`, `N_refine`, `refinement_lr`
2. **Second Priority**: `num_attractors_k`, `main_lr`, `w_plan`
3. **Hold Constant**: `d_model`, `w_verb`, architectural dimensions

### Appendix B: Document Validity Checklist

**Implementation Completeness Checklist**

**Architecture Implementation:**
- [ ] Each class stub in `source/` matches the documented interface signatures
- [ ] All tensor shapes and dtypes match API specifications (Section 5)
- [ ] Low-rank operator implementation follows Definition 4 (Section 3.2)
- [ ] Conceptual manifold data structure matches Section 5.2.1 specification

**Configuration Management:**
- [ ] All hyperparameters exist in `configs/` YAML files
- [ ] Parameter ranges match Table A1 specifications
- [ ] Runtime-tunable parameters are properly flagged
- [ ] Default values are set according to documentation

**Loss Functions:**
- [ ] All four loss components ($\mathcal{L}_{\mathrm{verb}}$, $\mathcal{L}_{\mathrm{plan}}$, $\mathcal{L}_{\mathrm{conv}}$, $\mathcal{L}_{\mathrm{place}}$) implemented
- [ ] Gradient routing logic matches Section 6.3 specifications
- [ ] Loss weighting follows documented formulations
- [ ] Placement loss includes both similarity and dependency terms

**Algorithm Implementation:**
- [ ] Algorithm 1 (Refinement Loop) matches pseudocode specification
- [ ] Algorithm 2 (Verbalization) includes proper termination conditions
- [ ] Algorithm 3 (Phase Manager) handles all status cases (SUCCESS, DIVERGED, NEEDS_SPLIT)
- [ ] Complexity bounds are respected in implementation

**Training Pipeline:**
- [ ] 5-step training pipeline implemented (Section 6.1)
- [ ] Batch processing handles variable-length operator sequences
- [ ] PyTorch training loop follows skeleton in Section 6.4
- [ ] Distributed training setup is DDP-compatible

**Evaluation Framework:**
- [ ] Benchmark evaluation suite covers Table 3 test cases
- [ ] Performance validation meets expected ranges
- [ ] Internal metrics (convergence rate, boundary F1) are tracked
- [ ] Ablation study framework is implemented

**Quality Assurance:**
- [ ] Unit tests assert correct tensor shapes and types
- [ ] Integration tests verify end-to-end generation pipeline
- [ ] Convergence thresholds are empirically validated
- [ ] Memory usage stays within expected bounds

### Appendix C: Architectural Deviations from Transformer

**Attention Mechanism Comparison**

The DELTA architecture's departure from the standard Transformer model includes a fundamentally different use of the attention mechanism.

**Table C1: Attention Usage Comparison**

| Attention Type | Standard Transformer | DELTA Architecture |
|----------------|---------------------|-------------------|
| **Self-Attention** | The core engine. Used in every block for all-to-all token communication within a sequence. | **Not used.** Replaced by recurrent networks (GRUs) for sequence processing and the dialectic loop for reasoning. |
| **Cross-Attention** | Used in encoder-decoder models for the decoder to attend to the encoder's output. | **Used for a single, specialized task**: The `SynthesisModule` uses cross-attention to fuse a `Thesis` vector (the query) with a set of `Antithesis` force vectors (the keys/values). |

**Key Distinctions:**
- DELTA does not use attention as its primary sequence processing mechanism
- Instead, it employs targeted cross-attention as a tool for **dynamic information fusion** within its core reasoning loop
- This represents a fundamentally different role than attention plays in standard Transformers
- The recurrent processing model allows for more explicit control over reasoning steps

**Implications:**
- Reduced computational complexity for long sequences
- More interpretable reasoning process through explicit dialectic steps
- Different scaling properties compared to standard attention mechanisms